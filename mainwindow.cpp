//
// Created by cap_s on 2025/6/25.
//

// You may need to build the project (run Qt uic code generator) to get "ui_MainWindow.h" resolved

#include "mainwindow.h"
#include "ui_MainWindow.h"
#include <QQmlEngine>
#include <QQmlContext>
#include <QApplication>
#include <QDir>
#include <QFile>
#include <QUrl>
#include <QMenu>
#include <QQuickItem>

MainWindow::MainWindow(QWidget *parent) :
    QMainWindow(parent), ui(new Ui::MainWindow) {
    ui->setupUi(this);
    setupUI();
    setupMenus();
}

MainWindow::~MainWindow() {
    delete ui;
}

void MainWindow::setupUI() {
    // 设置窗口标题和大小
    setWindowTitle("QML地图应用");
    resize(1000, 700);

    // 创建QQuickWidget来显示QML地图
    mapWidget = new QQuickWidget(this);
    mapWidget->setResizeMode(QQuickWidget::SizeRootObjectToView);

    // 设置QML文件路径
    QString qmlPath = QApplication::applicationDirPath() + "/mapview.qml";
    if (!QFile::exists(qmlPath)) {
        // 如果在应用程序目录找不到，尝试在源码目录
        qmlPath = QDir::currentPath() + "/mapview.qml";
    }

    mapWidget->setSource(QUrl::fromLocalFile(qmlPath));

    // 将地图控件设置为中央控件
    setCentralWidget(mapWidget);
}

void MainWindow::setupMenus() {
    // 创建菜单栏
    QMenuBar *menuBar = this->menuBar();
    QMenu *mapMenu = menuBar->addMenu("地图操作");

    // 创建北京Action
    beijingAction = new QAction("移动到北京", this);
    beijingAction->setShortcut(QKeySequence("Ctrl+B"));
    beijingAction->setStatusTip("将地图中心移动到北京");
    connect(beijingAction, &QAction::triggered, this, &MainWindow::moveToBeijing);
    mapMenu->addAction(beijingAction);

    // 创建测距Action
    measureAction = new QAction("测距", this);
    measureAction->setShortcut(QKeySequence("Ctrl+M"));
    measureAction->setStatusTip("测量两点之间的距离");
    connect(measureAction, &QAction::triggered, this, &MainWindow::startMeasure);
    mapMenu->addAction(measureAction);
}

void MainWindow::moveToBeijing() {
    // 调用QML中的函数
    QQuickItem *rootObject = mapWidget->rootObject();
    if (rootObject) {
        QMetaObject::invokeMethod(rootObject, "moveToBeijingLocation");
    }
}

void MainWindow::startMeasure() {
    // 调用QML中的函数
    QQuickItem *rootObject = mapWidget->rootObject();
    if (rootObject) {
        QMetaObject::invokeMethod(rootObject, "startMeasureMode");
    }
}
